<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.29.xsd"
        objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
    <changeSet id="1748674943737-9" author="obscurites">
        <createSequence incrementBy="1" sequenceName="mau_sac_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748674943737-10" author="obscurites">
        <createTable tableName="mau_sac">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_mau_sac"/>
            </column>
            <column name="mo_ta_mau_sac" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748674943737-11" author="obscurites">
        <addColumn tableName="san_pham_chi_tiet">
            <column name="mau_sac_id" type="BIGINT"/>
        </addColumn>
    </changeSet>
    <changeSet id="1748674943737-13" author="obscurites">
        <addNotNullConstraint columnName="mo_ta_danh_muc" tableName="danh_muc"/>
    </changeSet>
    <changeSet id="1748674943737-14" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="mau_sac_id" baseTableName="san_pham_chi_tiet"
                                 constraintName="FK_SAN_PHAM_CHI_TIET_ON_MAU_SAC" onDelete="SET NULL"
                                 referencedColumnNames="id" referencedTableName="mau_sac"/>
    </changeSet>
    <changeSet id="1748674943737-16" author="obscurites">
        <dropColumn columnName="mau_sac" tableName="san_pham_chi_tiet"/>
    </changeSet>

</databaseChangeLog>