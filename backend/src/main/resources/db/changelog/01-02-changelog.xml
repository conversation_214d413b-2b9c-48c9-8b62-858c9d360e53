<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.20.xsd">

    <changeSet id="add-inventory-locking-fields-001" author="system">
        <comment>Add inventory locking and timeout fields to san_pham_chi_tiet table</comment>
        
        <!-- Add reservation timestamp field -->
        <addColumn tableName="san_pham_chi_tiet">
            <column name="thoi_gian_dat_truoc" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        
        <!-- Add reservation channel field -->
        <addColumn tableName="san_pham_chi_tiet">
            <column name="kenh_dat_truoc" type="VARCHAR(20)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        
        <!-- Add order tracking field -->
        <addColumn tableName="san_pham_chi_tiet">
            <column name="don_hang_dat_truoc" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        
        <!-- Add index for reservation queries -->
        <createIndex tableName="san_pham_chi_tiet" indexName="idx_san_pham_chi_tiet_reservation">
            <column name="trang_thai"/>
            <column name="thoi_gian_dat_truoc"/>
        </createIndex>
        
        <!-- Add index for channel-based queries -->
        <createIndex tableName="san_pham_chi_tiet" indexName="idx_san_pham_chi_tiet_channel">
            <column name="trang_thai"/>
            <column name="kenh_dat_truoc"/>
        </createIndex>
        
        <!-- Add index for order tracking -->
        <createIndex tableName="san_pham_chi_tiet" indexName="idx_san_pham_chi_tiet_order">
            <column name="don_hang_dat_truoc"/>
        </createIndex>
        
        <rollback>
            <dropIndex tableName="san_pham_chi_tiet" indexName="idx_san_pham_chi_tiet_order"/>
            <dropIndex tableName="san_pham_chi_tiet" indexName="idx_san_pham_chi_tiet_channel"/>
            <dropIndex tableName="san_pham_chi_tiet" indexName="idx_san_pham_chi_tiet_reservation"/>
            <dropColumn tableName="san_pham_chi_tiet" columnName="don_hang_dat_truoc"/>
            <dropColumn tableName="san_pham_chi_tiet" columnName="kenh_dat_truoc"/>
            <dropColumn tableName="san_pham_chi_tiet" columnName="thoi_gian_dat_truoc"/>
        </rollback>
    </changeSet>

</databaseChangeLog>
