<template>
  <GenericAttributeManager :config="colorConfig" />
</template>

<script setup>
import GenericAttributeManager from './GenericAttributeManager.vue'

const colorConfig = {
  type: 'colors',
  label: '<PERSON><PERSON><PERSON> sắ<PERSON>',
  title: '<PERSON>u<PERSON>n lý <PERSON> sắ<PERSON>',
  description: '<PERSON>uản lý danh sách màu sắc sản phẩm trong hệ thống',
  tableTitle: '<PERSON><PERSON> sách M<PERSON> sắc',
  dialogTitle: 'Thông tin Màu sắc',
  icon: 'pi pi-palette',
  fieldName: 'moTaMauSac',
  fieldLabel: '<PERSON>ô tả Màu sắc'
}
</script>
