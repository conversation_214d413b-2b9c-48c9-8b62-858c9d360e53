<template>
  <GenericAttributeManager :config="storageConfig" />
</template>

<script setup>
import GenericAttributeManager from './GenericAttributeManager.vue'

const storageConfig = {
  type: 'storage',
  label: 'Ổ cứng',
  title: '<PERSON>u<PERSON>n lý Ổ cứng',
  description: '<PERSON>u<PERSON>n lý danh sách ổ cứng trong hệ thống',
  tableTitle: 'Danh sách Ổ cứng',
  dialogTitle: 'Thông tin Ổ cứng',
  icon: 'pi pi-database',
  fieldName: 'moTaOCung',
  fieldLabel: '<PERSON><PERSON> tả Ổ cứng'
}
</script>
